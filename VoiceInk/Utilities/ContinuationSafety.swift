import Foundation
import os.log

/// Utility class to help prevent double resume issues with CheckedContinuation
final class ContinuationSafety<T, E: Error> {
    private var hasResumed = false
    private let continuation: CheckedContinuation<T, E>
    private let logger = Logger(subsystem: "com.prakashjoshipax.VoiceInk", category: "continuation-safety")
    private let creationLocation: String
    private let timeout: TimeInterval?
    private var timeoutTask: Task<Void, Never>?

    init(_ continuation: CheckedContinuation<T, E>,
         file: String = #file,
         function: String = #function,
         line: Int = #line,
         timeout: TimeInterval? = nil) {
        self.continuation = continuation
        self.creationLocation = "\(file):\(function):\(line)"
        self.timeout = timeout

        // Set up timeout if specified
        if let timeout = timeout {
            timeoutTask = Task {
                try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                if !Task.isCancelled && !hasResumed {
                    logger.warning("⏰ Continuation timeout after \(timeout)s at \(self.creationLocation)")
                    let timeoutError = ContinuationTimeoutError(timeout: timeout, location: self.creationLocation)
                    if let error = timeoutError as? E {
                        resume(throwing: error)
                    } else {
                        logger.error("❌ Could not cast timeout error to expected type at \(self.creationLocation)")
                    }
                }
            }
        }
    }

    deinit {
        timeoutTask?.cancel()
        if !hasResumed {
            logger.warning("🚨 Continuation was never resumed at \(creationLocation)")
        }
    }

    /// Safely resume with a value, preventing double resume
    func resume(returning value: T) {
        guard !hasResumed else {
            logger.warning("⚠️ Attempted to resume continuation multiple times with value at \(creationLocation), ignoring")
            return
        }
        hasResumed = true
        timeoutTask?.cancel()
        continuation.resume(returning: value)
        logger.debug("✅ Continuation resumed with value at \(creationLocation)")
    }

    /// Safely resume with an error, preventing double resume
    func resume(throwing error: E) {
        guard !hasResumed else {
            logger.warning("⚠️ Attempted to resume continuation multiple times with error at \(creationLocation), ignoring")
            return
        }
        hasResumed = true
        timeoutTask?.cancel()
        continuation.resume(throwing: error)
        logger.debug("✅ Continuation resumed with error at \(creationLocation): \(error)")
    }

    /// Check if continuation has already been resumed
    var isResumed: Bool {
        return hasResumed
    }
}

/// Extension for non-throwing continuations
final class ContinuationSafetyNonThrowing<T> {
    private var hasResumed = false
    private let continuation: CheckedContinuation<T, Never>
    private let logger = Logger(subsystem: "com.prakashjoshipax.VoiceInk", category: "continuation-safety")
    private let creationLocation: String
    private let timeout: TimeInterval?
    private var timeoutTask: Task<Void, Never>?

    init(_ continuation: CheckedContinuation<T, Never>,
         file: String = #file,
         function: String = #function,
         line: Int = #line,
         timeout: TimeInterval? = nil) where T: ExpressibleByNilLiteral {
        self.continuation = continuation
        self.creationLocation = "\(file):\(function):\(line)"
        self.timeout = timeout

        // Set up timeout if specified
        if let timeout = timeout {
            timeoutTask = Task {
                try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                if !Task.isCancelled && !hasResumed {
                    logger.warning("⏰ Non-throwing continuation timeout after \(timeout)s at \(self.creationLocation)")
                    resume(returning: nil)
                }
            }
        }
    }

    deinit {
        timeoutTask?.cancel()
        if !hasResumed {
            logger.warning("🚨 Non-throwing continuation was never resumed at \(creationLocation)")
        }
    }

    /// Safely resume with a value, preventing double resume
    func resume(returning value: T) {
        guard !hasResumed else {
            logger.warning("⚠️ Attempted to resume non-throwing continuation multiple times at \(creationLocation), ignoring")
            return
        }
        hasResumed = true
        timeoutTask?.cancel()
        continuation.resume(returning: value)
        logger.debug("✅ Non-throwing continuation resumed with value at \(creationLocation)")
    }

    /// Check if continuation has already been resumed
    var isResumed: Bool {
        return hasResumed
    }
}

/// Error thrown when a continuation times out
struct ContinuationTimeoutError: Error, LocalizedError {
    let timeout: TimeInterval
    let location: String

    init(timeout: TimeInterval = 30.0, location: String = "unknown") {
        self.timeout = timeout
        self.location = location
    }

    var errorDescription: String? {
        return "Continuation timed out after \(timeout) seconds at \(location)"
    }
}

/// Convenience functions for safe continuation handling
extension CheckedContinuation {
    /// Create a safe wrapper for this continuation
    func makeSafe(file: String = #file,
                  function: String = #function,
                  line: Int = #line,
                  timeout: TimeInterval? = nil) -> ContinuationSafety<T, E> where E: Error {
        return ContinuationSafety(self, file: file, function: function, line: line, timeout: timeout)
    }
}

extension CheckedContinuation where E == Never {
    /// Create a safe wrapper for this non-throwing continuation
    func makeSafe(file: String = #file,
                  function: String = #function,
                  line: Int = #line,
                  timeout: TimeInterval? = nil) -> ContinuationSafetyNonThrowing<T> where T: ExpressibleByNilLiteral {
        return ContinuationSafetyNonThrowing(self, file: file, function: function, line: line, timeout: timeout)
    }
}

/// Convenience functions for creating safe continuations directly
func withSafeContinuation<T>(
    file: String = #file,
    function: String = #function,
    line: Int = #line,
    timeout: TimeInterval? = nil,
    _ body: (ContinuationSafetyNonThrowing<T>) -> Void
) async -> T where T: ExpressibleByNilLiteral {
    return await withCheckedContinuation { continuation in
        let safeContinuation = ContinuationSafetyNonThrowing(continuation, file: file, function: function, line: line, timeout: timeout)
        body(safeContinuation)
    }
}

func withSafeThrowingContinuation<T>(
    file: String = #file,
    function: String = #function,
    line: Int = #line,
    timeout: TimeInterval? = nil,
    _ body: (ContinuationSafety<T, Error>) -> Void
) async throws -> T {
    return try await withCheckedThrowingContinuation { continuation in
        let safeContinuation = ContinuationSafety(continuation, file: file, function: function, line: line, timeout: timeout)
        body(safeContinuation)
    }
}

/// Timeout error for async operations
struct AsyncTimeoutError: Error, LocalizedError {
    let timeout: TimeInterval

    var errorDescription: String? {
        return "Operation timed out after \(timeout) seconds"
    }
}

/// Execute an async operation with a timeout
func withTimeout<T>(
    seconds: TimeInterval,
    operation: @escaping () async throws -> T
) async throws -> T {
    return try await withThrowingTaskGroup(of: T.self) { group in
        // Add the main operation
        group.addTask {
            try await operation()
        }

        // Add the timeout task
        group.addTask {
            try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
            throw AsyncTimeoutError(timeout: seconds)
        }

        // Return the first result and cancel the other task
        defer { group.cancelAll() }
        return try await group.next()!
    }
}
