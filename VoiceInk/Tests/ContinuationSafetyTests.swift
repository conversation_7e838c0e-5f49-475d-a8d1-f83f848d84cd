import XCTest
import Foundation
@testable import VoiceInk

/// Comprehensive tests for continuation safety and crash prevention
final class ContinuationSafetyTests: XCTestCase {
    
    override func setUp() {
        super.setUp()
        // Reset any global state
    }
    
    override func tearDown() {
        super.tearDown()
        // Clean up any resources
    }
    
    // MARK: - Basic Continuation Safety Tests
    
    func testContinuationSafetyPreventsDubleResume() async throws {
        let expectation = XCTestExpectation(description: "Continuation should only resume once")
        var resumeCount = 0
        
        let result = await withSafeContinuation(timeout: 5.0) { safeContinuation in
            // Attempt to resume multiple times
            DispatchQueue.global().async {
                safeContinuation.resume(returning: "first")
                resumeCount += 1
            }
            
            DispatchQueue.global().asyncAfter(deadline: .now() + 0.1) {
                safeContinuation.resume(returning: "second")
                resumeCount += 1
                expectation.fulfill()
            }
        }
        
        await fulfillment(of: [expectation], timeout: 6.0)
        
        // Should only resume once with the first value
        XCTAssertEqual(result, "first")
        XCTAssertEqual(resumeCount, 2) // Both attempts were made
    }
    
    func testContinuationSafetyWithTimeout() async throws {
        let startTime = Date()
        
        do {
            let _ = await withSafeContinuation(timeout: 1.0) { safeContinuation in
                // Never resume - should timeout
            }
            XCTFail("Should have timed out")
        } catch {
            let elapsed = Date().timeIntervalSince(startTime)
            XCTAssertGreaterThan(elapsed, 0.9) // Should be close to 1 second
            XCTAssertLessThan(elapsed, 1.5) // But not too much longer
        }
    }
    
    func testThrowingContinuationSafety() async throws {
        enum TestError: Error {
            case testFailure
        }
        
        do {
            let _ = try await withSafeThrowingContinuation(timeout: 5.0) { safeContinuation in
                DispatchQueue.global().async {
                    safeContinuation.resume(throwing: TestError.testFailure)
                }
                
                // Attempt second resume - should be ignored
                DispatchQueue.global().asyncAfter(deadline: .now() + 0.1) {
                    safeContinuation.resume(returning: "should be ignored")
                }
            }
            XCTFail("Should have thrown an error")
        } catch TestError.testFailure {
            // Expected
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    // MARK: - Screen Capture Service Tests
    
    func testScreenCaptureServiceStressTest() async throws {
        let screenCaptureService = ScreenCaptureService()
        let iterations = 10
        var results: [String?] = []
        
        // Perform multiple rapid captures
        await withTaskGroup(of: String?.self) { group in
            for _ in 0..<iterations {
                group.addTask {
                    return await screenCaptureService.captureAndExtractText()
                }
            }
            
            for await result in group {
                results.append(result)
            }
        }
        
        // Should complete without crashing
        XCTAssertEqual(results.count, iterations)
        print("Screen capture stress test completed: \(results.count) operations")
    }
    
    func testScreenCaptureServiceCancellation() async throws {
        let screenCaptureService = ScreenCaptureService()
        
        let task = Task {
            return await screenCaptureService.captureAndExtractText()
        }
        
        // Cancel the task quickly
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.1) {
            task.cancel()
        }
        
        let result = await task.value
        // Should handle cancellation gracefully
        print("Screen capture with cancellation result: \(result ?? "nil")")
    }
    
    // MARK: - AI Enhancement Service Tests
    
    func testAIEnhancementServiceTaskManagement() async throws {
        let modelContext = try ModelContext(for: Transcription.self)
        let aiEnhancementService = AIEnhancementService(modelContext: modelContext)
        
        // Enable screen capture context
        await MainActor.run {
            aiEnhancementService.useScreenCaptureContext = true
            aiEnhancementService.isEnhancementEnabled = true
        }
        
        // Wait a bit for the capture task to start
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Rapidly toggle settings to test task cancellation
        await MainActor.run {
            aiEnhancementService.useScreenCaptureContext = false
            aiEnhancementService.useScreenCaptureContext = true
            aiEnhancementService.useScreenCaptureContext = false
        }
        
        // Should handle rapid changes without crashing
        print("AI Enhancement Service task management test completed")
    }
    
    // MARK: - Download Manager Tests
    
    func testDownloadWithTimeoutAndCancellation() async throws {
        // Create a mock URL that will be slow to respond
        let url = URL(string: "https://httpbin.org/delay/10")!
        
        let task = Task {
            return try await withSafeThrowingContinuation(timeout: 2.0) { safeContinuation in
                let downloadTask = URLSession.shared.downloadTask(with: url) { tempURL, response, error in
                    if let error = error {
                        safeContinuation.resume(throwing: error)
                    } else if let tempURL = tempURL {
                        safeContinuation.resume(returning: tempURL)
                    } else {
                        safeContinuation.resume(throwing: URLError(.badServerResponse))
                    }
                }
                downloadTask.resume()
            }
        }
        
        do {
            let _ = try await task.value
            XCTFail("Should have timed out")
        } catch {
            // Expected timeout or cancellation
            print("Download timeout test completed with error: \(error)")
        }
    }
    
    // MARK: - Timeout Utility Tests
    
    func testWithTimeoutSuccess() async throws {
        let result = try await withTimeout(seconds: 2.0) {
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
            return "success"
        }
        
        XCTAssertEqual(result, "success")
    }
    
    func testWithTimeoutFailure() async throws {
        do {
            let _ = try await withTimeout(seconds: 0.5) {
                try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
                return "should not reach here"
            }
            XCTFail("Should have timed out")
        } catch let error as AsyncTimeoutError {
            XCTAssertEqual(error.timeout, 0.5)
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    // MARK: - Integration Tests
    
    func testEnhancementModeIntegration() async throws {
        // This test simulates the crash scenario from the logs
        let modelContext = try ModelContext(for: Transcription.self)
        let aiEnhancementService = AIEnhancementService(modelContext: modelContext)
        
        await MainActor.run {
            aiEnhancementService.isEnhancementEnabled = true
            aiEnhancementService.useScreenCaptureContext = true
        }
        
        // Simulate multiple rapid operations that could cause race conditions
        let tasks = (0..<5).map { index in
            Task {
                await aiEnhancementService.captureScreenContext()
                return index
            }
        }
        
        // Wait for all tasks to complete
        var results: [Int] = []
        for task in tasks {
            let result = await task.value
            results.append(result)
        }
        
        XCTAssertEqual(results.count, 5)
        print("Enhancement mode integration test completed successfully")
    }
}
